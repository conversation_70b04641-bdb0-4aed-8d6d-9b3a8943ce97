import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import * as XLSX from 'xlsx';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';

const Aptitude = () => {
  const [jobs, setJobs] = useState([]);
  const [selectedIdx, setSelectedIdx] = useState(0);
  const [questions, setQuestions] = useState([]);
  const [fileName, setFileName] = useState('');
  const [manualQuestion, setManualQuestion] = useState({
    question: '',
    option1: '',
    option2: '',
    option3: '',
    option4: '',
    answer: '',
  });
  const [selectedAnswers, setSelectedAnswers] = useState({}); // { [questionIdx]: selectedOptionIdx }
  const [answerResults, setAnswerResults] = useState({}); // { [questionIdx]: 'right' | 'wrong' }
  const [showManualModal, setShowManualModal] = useState(false);
  const [testStarted, setTestStarted] = useState(false);
  const [currentQIdx, setCurrentQIdx] = useState(0);
  const [timer, setTimer] = useState(60);
  const [testFinished, setTestFinished] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    // Load jobs from localStorage
    const stored = localStorage.getItem('jobs');
    if (stored) {
      setJobs(JSON.parse(stored));
    }
  }, []);

  // Removed API fetch for questions. Questions must be loaded via file upload or manual entry only.

  const selectedJob = jobs[selectedIdx] || null;

  // Handle file upload and parse
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setFileName(file.name);
    const reader = new FileReader();
    reader.onload = (evt) => {
      const data = new Uint8Array(evt.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      // Expect header: job, question, option1, option2, option3, option4, answer
      const [header, ...rows] = json;
      const parsed = rows.map(row => {
        const obj = {};
        header.forEach((key, idx) => {
          obj[key] = row[idx];
        });
        return obj;
      });
      setQuestions(parsed);
    };
    reader.readAsArrayBuffer(file);
  };

  // Add manual question to questions state
  const handleManualSubmit = (e) => {
    e.preventDefault();
    if (!selectedJob) return;
    const newQuestion = {
      job: selectedJob.title,
      ...manualQuestion,
    };
    setQuestions(prev => [...prev, newQuestion]);
    
    // Optional: Save to backend for future use
    // saveQuestionToBackend(newQuestion);
    
    setManualQuestion({ question: '', option1: '', option2: '', option3: '', option4: '', answer: '' });
    setShowManualModal(false); // Close modal after adding
  };

  // Optional: Function to save questions to backend (for future use)
  const saveQuestionToBackend = async (questionData) => {
    try {
      await API.post('/api/questions', {
        questionText: questionData.question,
        option1: questionData.option1,
        option2: questionData.option2,
        option3: questionData.option3,
        option4: questionData.option4,
        answer: questionData.answer,
        job: questionData.job
      });
      console.log('✅ Question saved to backend');
    } catch (err) {
      console.error('❌ Error saving question to backend:', err);
      // Continue with local storage if backend fails
    }
  };

  // Filter questions for selected job
  const jobQuestions = selectedJob ? questions.filter(q => q.job && q.job.toLowerCase() === selectedJob.title.toLowerCase()) : [];

  // Handle answer selection
  const handleOptionSelect = (qIdx, optIdx) => {
    setSelectedAnswers(prev => ({ ...prev, [qIdx]: optIdx }));
    const q = jobQuestions[qIdx];
    if (q && q[`option${optIdx}`] && q.answer) {
      const isCorrect = q[`option${optIdx}`].toString().trim().toLowerCase() === q.answer.toString().trim().toLowerCase();
      setAnswerResults(prev => ({ ...prev, [qIdx]: isCorrect ? 'right' : 'wrong' }));
    }
  };

  // Timer effect
  useEffect(() => {
    if (!testStarted || testFinished || jobQuestions.length === 0) return;
    if (timer === 0) {
      if (currentQIdx < jobQuestions.length - 1) {
        setCurrentQIdx(currentQIdx + 1);
        setTimer(60);
      } else {
        setTestFinished(true);
      }
      return;
    }
    const interval = setInterval(() => setTimer(t => t - 1), 1000);
    return () => clearInterval(interval);
  }, [testStarted, timer, currentQIdx, jobQuestions.length, testFinished]);

  // Start test handler
  const handleStartTest = () => {
    setTestStarted(true);
    setCurrentQIdx(0);
    setTimer(60);
    setTestFinished(false);
    setSelectedAnswers({});
    setAnswerResults({});
  };

  return (
    <div className="min-h-screen w-full flex bg-gray-50">
      {/* Sidebar */}
      <aside className="fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] w-80 bg-gradient-to-b from-[rgb(35,65,75)] to-gray-900 border-r border-gray-900 shadow-2xl flex flex-col p-0">
        {/* Back Button (top right of sidebar) */}
        <button
          className="absolute top-4 right-4 flex items-center gap-2 px-3 py-2 bg-white/80 hover:bg-gray-200 text-gray-800 rounded-full shadow transition-all duration-200 font-semibold text-base z-50"
          onClick={() => navigate(-1)}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M7 12l10 0" /></svg>
          Back
        </button>
        <div className="px-8 py-7 border-b border-gray-800 text-2xl font-extrabold text-white tracking-tight flex items-center gap-3">
          <svg className="w-7 h-7 text-white opacity-80" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path d="M12 8v4l3 3" /></svg>
          Jobs
        </div>
        <nav className="flex-1 overflow-y-auto custom-scrollbar px-4 py-6">
          {jobs.length === 0 ? (
            <div className="text-gray-400 text-center mt-10 flex flex-col items-center gap-2">
              <svg className="w-12 h-12 text-gray-700/30" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path d="M12 8v4l3 3" /></svg>
              <span className="text-base font-medium">No jobs</span>
            </div>
          ) : (
            <ul className="flex flex-col gap-3">
              {jobs.map((job, idx) => (
                <li key={idx}>
                  <button
                    className={`w-full text-left px-6 py-4 rounded-xl transition-all duration-200 flex items-center gap-3 shadow-sm
                      ${selectedIdx === idx
                        ? 'bg-white/10 text-white font-bold shadow-lg ring-2 ring-blue-400/40 scale-[1.03]'
                        : 'text-gray-200 hover:bg-white/5 hover:text-white'}
                    `}
                    style={{ outline: selectedIdx === idx ? 'none' : undefined }}
                    onClick={() => setSelectedIdx(idx)}
                  >
                    <svg className={`w-4 h-4 ${selectedIdx === idx ? 'text-blue-400' : 'text-gray-500'}`} fill="currentColor" viewBox="0 0 20 20"><circle cx="10" cy="10" r="6" /></svg>
                    <span className="truncate text-lg">{job.title || 'Untitled Job'}</span>
                  </button>
                </li>
              ))}
            </ul>
          )}
        </nav>
      </aside>
      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center justify-start py-16 px-4 md:px-10 lg:px-24 transition-all duration-300">
        {selectedJob ? (
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 w-full max-w-2xl flex items-center justify-center mb-8 md:ml-32"
            initial={{ opacity: 0, y: 40, scale: 1.1 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 1.2, type: 'spring', stiffness: 60 }}
          >
            <h2 className="text-3xl font-bold text-gray-800 text-center">{selectedJob.title}</h2>
          </motion.div>
        ) : (
          <p className="text-lg text-gray-500 text-center mt-12">No jobs have been created yet.</p>
        )}
        {/* Upload/Manual Question Section - Modern Layout */}
        <motion.div
          className="flex flex-col items-center justify-center min-h-[60vh] w-full md:ml-32"
          initial="hidden"
          animate="visible"
          variants={{}}
        >
          <motion.div
            className="flex flex-col md:flex-row gap-8 w-full max-w-3xl justify-center"
            initial="hidden"
            animate="visible"
            variants={{ visible: { transition: { staggerChildren: 0.5 } } }}
          >
            {/* Upload Card */}
            <motion.div
              className="flex-1 bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center min-w-[280px] min-h-[340px]"
              initial={{ opacity: 0, y: 40, scale: 1.1 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1.2, type: 'spring', stiffness: 60 }}
            >
              <h2 className="text-xl font-bold text-[rgb(35,65,75)] mb-4 text-center">Upload Aptitude Questions (Excel):</h2>
              <label className="w-full flex flex-col items-center cursor-pointer">
                <input type="file" className="hidden" onChange={handleFileUpload} />
                <span className="inline-block px-6 py-2 bg-[rgb(35,65,75)] text-white rounded-lg font-semibold shadow hover:bg-[rgb(45,85,100)] mb-2 transition-all">Choose File</span>
                <span className="text-gray-500 text-sm mb-4">{fileName || 'No file chosen'}</span>
              </label>
              <div className="flex flex-wrap gap-2 justify-center mt-2">
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">job <span className="text-gray-500 font-normal">(must match the job title)</span></span>
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">question</span>
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">option1</span>
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">option2</span>
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">option3</span>
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">option4</span>
                <span className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-semibold text-xs">answer</span>
              </div>
            </motion.div>
            {/* OR Divider */}
            <div className="hidden md:flex flex-col justify-center items-center">
              <span className="text-gray-400 font-bold text-lg">or</span>
            </div>
            {/* Manual Add Card */}
            <motion.div
              className="flex-1 bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center min-w-[280px] min-h-[340px] justify-center"
              initial={{ opacity: 0, y: 40, scale: 1.1 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1.7, type: 'spring', stiffness: 60, delay: 0.5 }}
            >
              <button className="px-8 py-4 bg-[rgb(35,65,75)] text-white rounded-lg font-bold text-lg shadow hover:bg-[rgb(45,85,100)] transition-all" onClick={() => setShowManualModal(true)}>
                Add Question Manually
              </button>
            </motion.div>
          </motion.div>
        </motion.div>
        {showManualModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <motion.div className="relative bg-white rounded-2xl shadow-2xl p-0 w-full max-w-lg border-2 border-blue-200 animate-fade-in" initial={{ scale: 0.95, opacity: 0, y: 40 }} animate={{ scale: 1, opacity: 1, y: 0 }} transition={{ duration: 0.5, type: 'spring', stiffness: 120 }}>
              {/* Header */}
              <div className="flex items-center gap-3 rounded-t-2xl px-8 py-4" style={{ background: 'rgb(35, 65, 75)' }}>
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path d="M12 8v4l3 3" /></svg>
                <span className="text-white text-xl font-bold tracking-tight">Add Question Manually</span>
                <button
                  className="ml-auto text-blue-100 hover:text-white"
                  onClick={() => setShowManualModal(false)}
                  aria-label="Close"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>
              <form onSubmit={handleManualSubmit} className="flex flex-col gap-5 bg-gray-100 rounded-b-2xl px-8 py-8">
                <div>
                  <label className="block font-semibold text-blue-800 mb-1">Question</label>
                  <input
                    className="border border-blue-200 rounded px-3 py-2 w-full focus:ring-2 focus:ring-blue-300 focus:outline-none"
                    placeholder="Enter question"
                    value={manualQuestion.question}
                    onChange={e => setManualQuestion(q => ({ ...q, question: e.target.value }))}
                    required
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block font-semibold text-blue-800 mb-1">Option 1</label>
                    <input className="border border-blue-200 rounded px-3 py-2 w-full" placeholder="Option 1" value={manualQuestion.option1} onChange={e => setManualQuestion(q => ({ ...q, option1: e.target.value }))} required />
                  </div>
                  <div>
                    <label className="block font-semibold text-blue-800 mb-1">Option 2</label>
                    <input className="border border-blue-200 rounded px-3 py-2 w-full" placeholder="Option 2" value={manualQuestion.option2} onChange={e => setManualQuestion(q => ({ ...q, option2: e.target.value }))} required />
                  </div>
                  <div>
                    <label className="block font-semibold text-blue-800 mb-1">Option 3</label>
                    <input className="border border-blue-200 rounded px-3 py-2 w-full" placeholder="Option 3" value={manualQuestion.option3} onChange={e => setManualQuestion(q => ({ ...q, option3: e.target.value }))} required />
                  </div>
                  <div>
                    <label className="block font-semibold text-blue-800 mb-1">Option 4</label>
                    <input className="border border-blue-200 rounded px-3 py-2 w-full" placeholder="Option 4" value={manualQuestion.option4} onChange={e => setManualQuestion(q => ({ ...q, option4: e.target.value }))} required />
                  </div>
                </div>
                <div>
                  <label className="block font-semibold text-blue-800 mb-1">Answer</label>
                  <input className="border border-blue-200 rounded px-3 py-2 w-full" placeholder="Enter correct answer" value={manualQuestion.answer} onChange={e => setManualQuestion(q => ({ ...q, answer: e.target.value }))} required />
                </div>
                <button type="submit" className="mt-2 px-8 py-3 bg-green-600 text-white rounded-xl font-bold shadow hover:bg-green-700 transition-colors text-lg"style={{ background: 'rgb(35, 65, 75)' }}>Add Question</button>
              </form>
            </motion.div>
          </div>
        )}
        {/* Start Test Button and Test UI */}
        {testStarted && jobQuestions.length > 0 && !testFinished && (
          <motion.div className="w-full max-w-2xl mt-8 flex flex-col gap-6" initial={{ opacity: 0, y: 40 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.4, type: 'spring', stiffness: 80 }}>
            <div className="flex justify-between items-center mb-4">
              <span className="text-lg font-semibold text-blue-700">Question {currentQIdx + 1} of {jobQuestions.length}</span>
              <span className="text-lg font-bold text-red-600">{timer}s</span>
            </div>
            <motion.div className="bg-gray-100 rounded-lg shadow p-6 border border-gray-100" initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, type: 'spring', stiffness: 100 }}>
              <div className="font-semibold text-lg text-gray-800 mb-2">Q{currentQIdx + 1}. {jobQuestions[currentQIdx].question}</div>
              <ul className="flex flex-col gap-2 mt-2">
                {[1,2,3,4].map(i => (
                  jobQuestions[currentQIdx][`option${i}`] && (
                    <li key={i}>
                      <button
                        className={`w-full text-left px-4 py-2 rounded border transition-colors duration-200
                          ${selectedAnswers[currentQIdx] === i
                            ? (answerResults[currentQIdx] === 'right' ? 'bg-green-100 border-green-400 text-green-800 font-semibold' : 'bg-red-100 border-red-400 text-red-800 font-semibold')
                            : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-blue-50'}
                        `}
                        onClick={() => handleOptionSelect(currentQIdx, i)}
                        disabled={selectedAnswers[currentQIdx] !== undefined}
                      >
                        {jobQuestions[currentQIdx][`option${i}`]}
                      </button>
                    </li>
                  )
                ))}
              </ul>
              {selectedAnswers[currentQIdx] !== undefined && (
                <div className={`mt-3 font-semibold ${answerResults[currentQIdx] === 'right' ? 'text-green-600' : 'text-red-600'}`}>
                  {answerResults[currentQIdx] === 'right' ? 'Correct!' : 'Wrong!'}
                </div>
              )}
              {/* Next button if answered before time runs out */}
              {selectedAnswers[currentQIdx] !== undefined && currentQIdx < jobQuestions.length - 1 && (
                <button
                  className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                  onClick={() => { setCurrentQIdx(currentQIdx + 1); setTimer(60); }}
                >
                  Next
                </button>
              )}
              {selectedAnswers[currentQIdx] !== undefined && currentQIdx === jobQuestions.length - 1 && (
                <button
                  className="mt-4 px-6 py-2 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors"style={{ background: 'rgb(35, 65, 75)' }}
                  onClick={() => setTestFinished(true)}
                >
                  Finish Test
                </button>
              )}
            </motion.div>
          </motion.div>
        )}
        {testFinished && (
          <motion.div className="w-full max-w-2xl mt-8 flex flex-col items-center" initial={{ opacity: 0, y: 40 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.5, type: 'spring', stiffness: 80 }}>
            <motion.div className="bg-gray-100 rounded-xl shadow border border-gray-200 p-8 text-center" initial={{ scale: 0.95, opacity: 0, y: 30 }} animate={{ scale: 1, opacity: 1, y: 0 }} transition={{ duration: 0.5, type: 'spring', stiffness: 100 }}>
              <div className="text-2xl font-bold text-green-700 mb-2">Test Finished!</div>
              <div className="text-lg text-gray-700">You have completed the test.</div>
              <button
                className="mt-6 px-8 py-3 bg-blue-600 text-white rounded-xl font-bold shadow hover:bg-blue-700 transition-colors text-lg"style={{ background: 'rgb(35, 65, 75)' }}
                onClick={() => { setTestStarted(false); setTestFinished(false); setCurrentQIdx(0); setTimer(60); setSelectedAnswers({}); setAnswerResults({}); }}
              >
                Restart Test
              </button>
            </motion.div>
          </motion.div>
        )}
      </main>
    </div>
  );
};

export default Aptitude;
