// src/pages/CreateJob/CreateJob.js
import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../../store/authStore';
import useCompanyStore from '../../store/companyStore';
import { motion } from 'framer-motion';
import JobFormModal from './components/JobFormModal';
import ExistingJobsSection from './components/ExistingJobsSection';
import {
  ArrowLeftIcon,
  PlusCircleIcon,
  BriefcaseIcon,
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/solid';

const CreateJob = () => {
  const navigate = useNavigate();
  const { user, fetchCurrentUser } = useAuthStore();
  const { createJob, updateJob, getJobs, jobs } = useCompanyStore();

  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    title: '',
    description: '',
    requirements: [],
    techStack: [],
    category: '',
    jobType: 'Full-time',
    experienceLevel: 'Mid',
    salary: { min: 0, max: 0, currency: 'INR' },
    location: '',
    workMode: 'Remote',
    applicationDeadline: '',
    maxApplications: 100,
    hasTest: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [selectedJob, setSelectedJob] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  const checkUserAuth = useCallback(async () => {
    try {
      setIsCheckingAuth(true);
      const token = localStorage.getItem('token');
      if (!token) return navigate('/login');

      let currentUser = user || (await fetchCurrentUser());
      if (!currentUser || currentUser.role !== 'company') return navigate('/');
    } catch {
      navigate('/login');
    } finally {
      setIsCheckingAuth(false);
    }
  }, [user, fetchCurrentUser, navigate]);

  useEffect(() => {
    checkUserAuth();
    getJobs();
  }, [checkUserAuth, getJobs]);

  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    if (error) setError(null);
  }, [error]);

  const handleRequirementChange = (e) => {
    setForm((prev) => ({
      ...prev,
      requirements: e.target.value.split(',').map((s) => s.trim()).filter(s => s),
    }));
  };

  const handleTechStackChange = (e) => {
    setForm((prev) => ({
      ...prev,
      techStack: e.target.value.split(',').map((s) => s.trim()).filter(s => s),
    }));
  };

  const validateForm = useCallback(() => {
    const requiredFields = [
      'title',
      'description',
      'category',
      'jobType',
      'experienceLevel',
      'location',
      'workMode',
      'applicationDeadline',
    ];
    const emptyFields = requiredFields.filter((field) => !form[field]?.toString().trim());
    if (emptyFields.length > 0) return `Missing required fields: ${emptyFields.join(', ')}`;

    const { min, max } = form.salary;
    if (min < 0 || max < 0) return 'Salary cannot be negative';
    if (min >= max && max > 0) return 'Minimum salary must be less than maximum salary';
    if (form.maxApplications < 1) return 'Maximum applications must be at least 1';

    const deadline = new Date(form.applicationDeadline);
    if (deadline < new Date()) return 'Application deadline must be in the future';

    return null;
  }, [form]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      setLoading(false);
      return;
    }

    try {
      const jobData = {
        ...form,
        salary: {
          min: parseFloat(form.salary.min) || 0,
          max: parseFloat(form.salary.max) || 0,
          currency: form.salary.currency,
        },
        applicationDeadline: new Date(form.applicationDeadline).toISOString(),
        maxApplications: parseInt(form.maxApplications) || 100,
      };

      let res;
      if (selectedJob) {
        res = await updateJob(selectedJob._id, jobData);
      } else {
        res = await createJob(jobData);
      }

      if (res && !res.error) {
        setSuccess(true);
        setSelectedJob(null);
        resetForm();
        setTimeout(() => {
          setSuccess(false);
          setShowForm(false);
        }, 2000);
      } else {
        setError(res?.error || `Job ${selectedJob ? 'update' : 'creation'} failed`);
      }
    } catch (err) {
      setError('Server error. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setForm({
      title: '',
      description: '',
      requirements: [],
      techStack: [],
      category: '',
      jobType: 'Full-time',
      experienceLevel: 'Mid',
      salary: { min: 0, max: 0, currency: 'INR' },
      location: '',
      workMode: 'Remote',
      applicationDeadline: '',
      maxApplications: 100,
      hasTest: false,
    });
  };

  const handleEdit = (job) => {
    setSelectedJob(job);
    setForm({
      title: job.title || '',
      description: job.description || '',
      requirements: job.requirements || [],
      techStack: job.techStack || [],
      category: job.category || '',
      jobType: job.jobType || 'Full-time',
      experienceLevel: job.experienceLevel || 'Mid',
      salary: job.salary || { min: 0, max: 0, currency: 'INR' },
      location: job.location || '',
      workMode: job.workMode || 'Remote',
      applicationDeadline: job.applicationDeadline ? new Date(job.applicationDeadline).toISOString().slice(0, 16) : '',
      maxApplications: job.maxApplications || 100,
      hasTest: job.hasTest || false,
    });
    setShowForm(true);
  };

  const handleCloseForm = useCallback(() => {
    setShowForm(false);
    setSelectedJob(null);
    setError(null);
    setSuccess(false);
    resetForm();
  }, []);

  const handleKeyDown = useCallback(
    (e) => {
      if (e.key === 'Escape' && showForm) handleCloseForm();
    },
    [showForm, handleCloseForm]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Filter jobs based on search and category
  const filteredJobs = jobs?.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !filterCategory || job.category === filterCategory;
    return matchesSearch && matchesCategory;
  }) || [];

  // Statistics
  const stats = {
    totalJobs: jobs?.length || 0,
    activeJobs: jobs?.filter(job => new Date(job.applicationDeadline) > new Date()).length || 0,
    totalApplications: jobs?.reduce((sum, job) => sum + (job.applications || 0), 0) || 0,
  };

  if (isCheckingAuth) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-400 rounded-full animate-pulse mx-auto"></div>
          </div>
          <p className="text-lg font-semibold text-gray-700">Checking access...</p>
          <p className="text-sm text-gray-500 mt-1">Please wait while we verify your credentials</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Dashboard
            </button>

            <div className="hidden sm:block">
              <p className="text-sm text-gray-600">
                Welcome back, <span className="font-semibold text-blue-600">{user?.name || user?.email}</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
            <BriefcaseIcon className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Manage Your job
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Create compelling job postings and manage your recruitment pipeline with ease
          </p>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-8">
            <motion.div
              className="bg-white rounded-xl shadow-sm border p-6"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
                <BriefcaseIcon className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">{stats.totalJobs}</h3>
              <p className="text-gray-600">Total Jobs Posted</p>
            </motion.div>

            <motion.div
              className="bg-white rounded-xl shadow-sm border p-6"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">{stats.activeJobs}</h3>
              <p className="text-gray-600">Active Listings</p>
            </motion.div>

            <motion.div
              className="bg-white rounded-xl shadow-sm border p-6"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-3">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">{stats.totalApplications}</h3>
              <p className="text-gray-600">Total Applications</p>
            </motion.div>
          </div>

          <motion.button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.98 }}
          >
            <PlusCircleIcon className="h-5 w-5" />
            Create New Job Posting
          </motion.button>
        </motion.div>

        {/* Job Form Modal */}
        {showForm && (
          <JobFormModal
            showForm={showForm}
            handleCloseForm={handleCloseForm}
            handleSubmit={handleSubmit}
            handleChange={handleChange}
            handleRequirementChange={handleRequirementChange}
            handleTechStackChange={handleTechStackChange}
            form={form}
            error={error}
            success={success}
            loading={loading}
            isEditing={!!selectedJob}
          />
        )}

        {/* Existing Jobs Section */}
        <ExistingJobsSection
          jobs={filteredJobs}
          onEdit={handleEdit}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          filterCategory={filterCategory}
          setFilterCategory={setFilterCategory}
        />
      </div>
    </div>
  );
};

export default CreateJob;