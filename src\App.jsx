import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Navbar from "./Components/Navbar.jsx";
import Hero from "./Components/Hero.jsx";
import AnimatedCardsSection from "./Components/AnimatedCardsSection.jsx";
import About from "./Components/About.jsx";
import HowItWorks from "./Components/HowItWorks.jsx";
import Testimonials from "./Components/Testimonials.jsx";
import Footer from "./Components/Footer.jsx";
import Layout from "./Components/Layout.jsx";
import CompanyDashboard from "./Components/company/CompanyDashboard.jsx";
import CreateJob from "./Components/company/Createjob.jsx";
import Aptitude from "./Components/company/Aptitude.jsx";
import Interview from "./Components/company/Interview.jsx";
import Profile from "./Components/company/Profile.jsx";
// import CompanyLogin from './Components/company/CompanyLogin';
import RegistrationPage from "./pages/RegistationPage.jsx";
import VerifyOtp from "./pages/VerifyOtp.jsx";
import LoginPage from "./pages/LoginPage.jsx";
function LandingPage() {
  return (
    <div className="font-sans text-gray-800 bg-[#f7f8fa]">
      <Navbar />
      <main>
        <section className="min-h-[85vh] flex items-center justify-center bg-white">
          <Hero />
        </section>
        {/* Animated Cards Section */}
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto bg-white">
            <AnimatedCardsSection />
          </div>
        </section>
        {/* About & Services */}
        <section className="py-20 px-4 bg-white">
          <div className="">
            <About />
          </div>
        </section>
        {/* How It Works */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <HowItWorks />
          </div>
        </section>
        {/* Testimonials */}
        <section className="py-20 px-4">
          <div className="">
            <Testimonials dark={false} />
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        {/* <Route path="/company-login" element={<CompanyLogin />} /> */}
        <Route path="/register" element={<RegistrationPage />} />
        <Route path="/verify-otp" element={<VerifyOtp />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/" element={<Layout />}>
          <Route path="dashboard" element={<CompanyDashboard />} />
          <Route path="job-create" element={<CreateJob />} />
          <Route path="aptitude" element={<Aptitude />} />
          <Route path="interview" element={<Interview />} />
          <Route path="profile" element={<Profile />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
