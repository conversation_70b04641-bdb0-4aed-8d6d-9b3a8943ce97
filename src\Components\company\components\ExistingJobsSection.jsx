// src/components/ExistingJobsSection.js
import React from 'react';
import JobCard from './JobCard';

const ExistingJobsSection = ({ jobs, onEdit }) => {
    if (!jobs || jobs.length === 0) {
        return (
            <div className="text-center text-gray-500 mt-8">
                No jobs posted yet. Create a new job to get started.
            </div>
        );
    }

    return (
        <div className="grid md:grid-cols-2 gap-6 mt-8">
            {jobs.map((job) => (
                <JobCard key={job._id} job={job} onEdit={onEdit} />
            ))}
        </div>
    );
};

export default ExistingJobsSection;